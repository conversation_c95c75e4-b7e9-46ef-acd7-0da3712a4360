<?php /*a:1:{s:67:"/var/www/html/application/manage/view/bank/withdrawals_details.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提现详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单编号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="order_number" value="<?php echo htmlentities($data['order_number']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">提款金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="" value="<?php echo htmlentities($data['price']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">提款信息</label>
                                <div class="layui-input-block">
                                    <input type="text" name="" value="<?php echo htmlentities($data['bank_id']); ?>——<?php echo htmlentities($data['card_name']); ?>——<?php echo htmlentities($data['card_number']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理结果</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="state" value="1" title="已支付"<?php if($data['state'] == 1): ?> checked<?php endif; ?> disabled>
                                    <input type="radio" name="state" value="2" title="拒绝支付"<?php if($data['state'] == 2): ?> checked<?php endif; ?> disabled>
                                    <input type="radio" name="state" value="3" title="审核中"<?php if($data['state'] == 3): ?> checked<?php endif; ?> disabled>
                                    <input type="radio" name="state" value="4" title="待支付"<?php if($data['state'] == 4): ?> checked<?php endif; ?> disabled>
                                    <input type="radio" name="state" value="7" title="代付中"<?php if($data['state'] == 7): ?> checked<?php endif; ?> disabled>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理说明</label>
                                <div class="layui-input-block">
                                    <textarea name="remarks" placeholder="处理说明" class="layui-textarea" readonly style="min-height: 80px;"><?php echo isset($data['remarks']) ? htmlentities($data['remarks']) : ''; ?></textarea>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>

                            <!-- 添加状态说明 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">状态说明</label>
                                <div class="layui-input-block">
                                    <div class="layui-text" style="padding: 10px; background-color: #f8f8f8; border-radius: 4px;">
                                        <?php switch($data['state']): case "1": ?>
                                                <span style="color: #5FB878;">✓ 已支付</span> - 提现已成功处理并支付给用户
                                            <?php break; case "2": ?>
                                                <span style="color: #FF5722;">✗ 拒绝支付</span> - 提现申请被拒绝，资金已退回用户账户
                                            <?php break; case "3": ?>
                                                <span style="color: #FFB800;">⏳ 审核中</span> - 提现申请正在审核中，等待管理员处理
                                            <?php break; case "4": ?>
                                                <span style="color: #1E9FFF;">💰 待支付</span> - 审核通过，等待执行支付操作
                                            <?php break; case "7": ?>
                                                <span style="color: #01AAED;">🔄 代付中</span> - 正在通过第三方渠道执行代付
                                            <?php break; default: ?>
                                                <span style="color: #666;">未知状态</span>
                                        <?php endswitch; ?>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script>
// 简化的脚本，避免加载可能有问题的bank.js
layui.use(['form'], function(){
    var form = layui.form;
    form.render();
});
</script>
</body>
</html>