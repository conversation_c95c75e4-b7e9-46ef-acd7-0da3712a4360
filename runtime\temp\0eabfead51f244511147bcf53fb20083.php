<?php /*a:1:{s:62:"/var/www/html/application/manage/view/bank/present_record.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>提现记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form search">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">更新</label>
                                <div class="layui-input-inline">
                                    <select class="recharge-withdraw-reload" lay-filter="recharge-withdraw-reload" data-reloadType="present_record">
                                        <option value="0">暂停</option>
                                        <option value="15">15秒</option>
                                        <option value="30">30秒</option>
                                        <option value="60">60秒</option>
                                    </select>
                                </div>
                            </div>
                            
                              <div class="layui-inline">
                                <label class="layui-form-label">账号类型</label>
                                <div class="layui-input-inline">
                                    <select name="user_type" class="recharge-withdraw-reload" lay-filter="recharge-withdraw-reload" data-reloadType="recharge_record">
                                          <option value="0">全部</option>
                                        <option value="1">代理</option>
                                        <option value="2">会员</option>
                                        <option value="3">测试</option>
                                    
                                    </select>
                                </div>
                            </div>
                            
                            <div class="layui-inline">
                                <label class="layui-form-label">搜索类型</label>
                                <div class="layui-input-inline">
                                    <select name="search_t" lay-search="">
                                        <option value="username">取款账号</option>
                                        <option value="order_number">订单号</option>
                                        <option value="card_name">取款姓名</option>
                                        <option value="card_number">取款卡号</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">内容</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="search_c" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-inline">
                                    <select name="state" lay-search="">
                                        <?php foreach(app('config')->get('custom.withdrawalsState') as $key=>$value): ?>
                                        <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($value); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="datetime_range" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-block" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="search">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="present_record" lay-filter="present_record"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="batchAudit">批量审核</button>
            <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchWithdrawal">批量代付</button>
        </div>
    </script>

    <!-- 表单元素 -->
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            {{# if (d.state == 3) { }}
            <!-- 审核中状态 -->
            <button type="button" class="layui-btn layui-btn-xs" lay-event="controlAudit">审核</button>
            {{# } else if (d.state == 4) { }}
            <!-- 待支付状态 -->
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="executePayment">支付</button>
            <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" lay-event="withdrawalsDetails">详情</button>
            {{# } else if (d.state == 5) { }}
            <!-- 代付中状态 -->
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="paymentAction">操作</button>
            <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" lay-event="withdrawalsDetails">详情</button>
            {{# } else if (d.state == 6) { }}
            <!-- 出款成功状态，只显示详情 -->
            <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" lay-event="withdrawalsDetails">详情</button>
            {{# } else if (d.state == 7) { }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-warm" lay-event="withdrawalsDetails">代付中</button>
            {{# } else { }}
            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="withdrawalsDetails">详情</button>
            {{# } }}
        </div>
    </script>

    <!-- 批量审核弹窗 -->
    <div id="batchAuditModal" style="display: none; padding: 20px;">
        <form class="layui-form" id="batchAuditForm">
            <div class="layui-form-item">
                <label class="layui-form-label">处理结果</label>
                <div class="layui-input-block">
                    <input type="radio" name="examine" value="1" title="审核通过" checked>
                    <input type="radio" name="examine" value="2" title="审核未通过">
                    <input type="radio" name="examine" value="3" title="待审核">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="confirmBatchAudit">确认处理</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 批量代付渠道选择弹窗 -->
    <div id="channelSelectModal" style="display: none; padding: 20px;">
        <form class="layui-form" id="channelSelectForm">
            <div class="layui-form-item">
                <label class="layui-form-label">代付渠道</label>
                <div class="layui-input-block">
                    <select name="channel_id" lay-verify="required" lay-search="">
                        <option value="">请选择代付渠道</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="confirmBatchWithdrawal">确认代付</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 音频 -->
    <audio id="myaudio" src="/resource/media/present_record.mp3" hidden="true">

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
<script>
    layui.use(['table', 'form'], function(){
        var $ = layui.$
        ,table = layui.table
        ,form = layui.form;

        //方法级渲染
        table.render({
            elem: '#present_record'
            ,title: '提现记录'
            ,url: '/manage/bank/present_record'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'order_number', title: '订单号', sort: true,width:250}
                ,{field: 'aname', title: '财务', sort: true}
                ,{field: 'username', title: '账号', sort: true}
                ,{field: 'vip_display_name', title: 'VIP等级', sort: true, templet: function(d){
                    var vipClass = '';
                    if (d.vip_status === '有效') {
                        vipClass = 'color: #009688; font-weight: bold;';
                    } else if (d.vip_status === '已过期') {
                        vipClass = 'color: #ff5722;';
                    } else {
                        vipClass = 'color: #999;';
                    }
                    return '<span style="' + vipClass + '">' + (d.vip_display_name || 'VIP1') + '</span>';
                }}
                ,{field: 'danger', title: '风险账户', sort: true, templet: function(d){
                    return d.danger == 1 ? '<b style="color: red;">是</b>' : '否';
                }}
                ,{field: 'bank_name', title: '提现银行', sort: true}
                ,{field: 'card_number', title: '取款账号', sort: true,width:400}
                ,{field: 'card_name', title: '户名', sort: true}
                ,{field: 'channel_name', title: '代付渠道', sort: true, templet: function(d){
                    return d.channel_name || '未选择';
                }}
                ,{field: 'price', title: '取款金额', sort: true, totalRow: true}
                ,{field: 'fee', title: '服务费', sort: true, totalRow: true}
                ,{field: 'state', title: '状态', sort: true, templet: function(d){
                    return d.statusStr;
                }}
                ,{field: 'time', title: '提交时间', sort: true,width:200}
                ,{title: '操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(present_record)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('present_record', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });

        // 加载代付渠道列表（批量代付只支持第三方代付）
        function loadWithdrawalChannels() {
            $.get('/manage/withdrawal_channel/getEnabledChannels', function(res) {
                if(res.code == 1) {
                    var options = '<option value="">请选择代付渠道</option>';
                    var thirdPartyChannels = [];

                    // 过滤出第三方代付渠道（排除传统代付）
                    $.each(res.data, function(index, item) {
                        if(item.mode !== 'traditional') {
                            thirdPartyChannels.push(item);
                            options += '<option value="' + item.id + '">' + item.name + ' (' + item.mode + ')</option>';
                        }
                    });

                    // 如果没有可用的第三方代付渠道，显示提示
                    if(thirdPartyChannels.length === 0) {
                        options = '<option value="">暂无可用的第三方代付渠道</option>';
                    }

                    $('#channelSelectModal select[name="channel_id"]').html(options);
                    form.render('select');
                }
            }, 'json');
        }

        // 页面加载时获取渠道列表
        loadWithdrawalChannels();

        // 批量审核变量
        var batchAuditType = '';
        var batchAuditIds = '';

        // 批量代付变量
        var batchWithdrawalIds = '';

        //监听头工具栏事件
        table.on('toolbar(present_record)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            var data = checkStatus.data;

            switch(obj.event){
                case 'batchAudit':
                    if(data.length === 0){
                        layer.msg('请选择要审核的记录');
                        return;
                    }
                    batchAuditType = 'audit';
                    batchAuditIds = data.map(function(item){ return item.id; }).join(',');

                    // 弹出批量审核窗口
                    layer.open({
                        type: 1,
                        title: '批量审核',
                        content: $('#batchAuditModal'),
                        area: ['400px', '250px'],
                        btn: false,
                        closeBtn: 1
                    });
                    break;
                case 'batchWithdrawal':
                    if(data.length === 0){
                        layer.msg('请选择要代付的记录');
                        return;
                    }
                    batchWithdrawalIds = data.map(function(item){ return item.id; }).join(',');

                    // 弹出代付渠道选择窗口
                    layer.open({
                        type: 1,
                        title: '选择代付渠道',
                        content: $('#channelSelectModal'),
                        area: ['400px', '250px'],
                        btn: false,
                        closeBtn: 1
                    });
                    break;
            }
        });

        // 监听批量审核确认
        form.on('submit(confirmBatchAudit)', function(data){
            var examine = data.field.examine;

            var url = '';
            var confirmMsg = '';
            var examineText = {'1': '审核通过', '2': '审核未通过', '3': '待审核'}[examine] || '处理';

            if(batchAuditType === 'audit') {
                url = '/manage/bank/batchAudit';
                confirmMsg = '确定要批量' + examineText + '这些记录吗？';
            }

            layer.confirm(confirmMsg, function(index){
                layer.closeAll(); // 关闭弹窗
                var loadIndex = layer.load(2);

                $.post(url, {
                    ids: batchAuditIds,
                    examine: examine
                }, function(res){
                    layer.close(loadIndex);
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                        table.reload('present_record');
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }, 'json');
                layer.close(index);
            });

            return false;
        });

        // 监听批量代付渠道选择确认
        form.on('submit(confirmBatchWithdrawal)', function(data){
            var channelId = data.field.channel_id;

            if(!channelId) {
                layer.msg('请选择代付渠道');
                return false;
            }

            // 检查是否选择了无效选项
            var selectedText = $('#channelSelectModal select[name="channel_id"] option:selected').text();
            if(selectedText.indexOf('暂无可用') !== -1) {
                layer.msg('暂无可用的第三方代付渠道，批量代付功能不可用');
                return false;
            }

            layer.confirm('确定要使用选择的渠道批量执行代付吗？<br><span style="color: #ff5722;">注意：批量代付仅支持第三方代付渠道</span>', function(index){
                layer.closeAll(); // 关闭弹窗
                var loadIndex = layer.load(2);

                $.post('/manage/bank/batchWithdrawal', {
                    ids: batchWithdrawalIds,
                    channel_id: channelId
                }, function(res){
                    layer.close(loadIndex);
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                        table.reload('present_record');
                    }else{
                        // 批量代付失败时也关闭弹窗并刷新列表
                        layer.msg(res.msg, {icon: 2}, function(){
                            layer.closeAll();
                            table.reload('present_record');
                        });
                    }
                }, 'json');
                layer.close(index);
            });

            return false;
        });

        //监听行工具事件（单个操作）
        table.on('tool(present_record)', function(obj){
            var data = obj.data;
            if(obj.event === 'executePayment'){
                // 单个支付操作
                layer.open({
                    type: 2,
                    title: '执行支付',
                    content: '/manage/bank/controlAudit?order_number=' + data.order_number,
                    area: ['90%', '90%'],
                    end: function(){
                        // 弹窗关闭时刷新列表
                        table.reload('present_record');
                    }
                });
            } else if(obj.event === 'withdrawalsDetails'){
                // 查看详情
                layer.open({
                    type: 2,
                    title: '提现详情',
                    content: '/manage/bank/withdrawalsDetails?order_number=' + data.order_number,
                    area: ['90%', '90%']
                });
            } else if(obj.event === 'controlAudit'){
                // 审核操作
                layer.open({
                    type: 2,
                    title: '提现审核',
                    content: '/manage/bank/controlAudit?order_number=' + data.order_number,
                    area: ['90%', '90%'],
                    end: function(){
                        table.reload('present_record');
                    }
                });
            } else if(obj.event === 'paymentAction'){
                // 代付中状态的操作选择
                console.log('paymentAction clicked', data);
                var actionHtml = '<div style="padding: 20px;">';
                actionHtml += '<form class="layui-form" id="paymentActionForm">';
                actionHtml += '<div style="text-align: center; margin-bottom: 20px;">';
                actionHtml += '<h3>代付操作</h3>';
                actionHtml += '<p style="color: #666;">订单号：' + data.order_number + '</p>';
                actionHtml += '<p style="color: #666;">金额：￥' + data.price + '</p>';
                actionHtml += '</div>';
                actionHtml += '<div class="layui-form-item">';
                actionHtml += '<label class="layui-form-label">操作类型</label>';
                actionHtml += '<div class="layui-input-block">';
                actionHtml += '<input type="radio" name="action_type" value="success" title="支付成功" lay-filter="actionType">';
                actionHtml += '<input type="radio" name="action_type" value="failed" title="支付失败" lay-filter="actionType">';
                actionHtml += '</div>';
                actionHtml += '</div>';
                actionHtml += '<div class="layui-form-item">';
                actionHtml += '<label class="layui-form-label">操作原因</label>';
                actionHtml += '<div class="layui-input-block">';
                actionHtml += '<textarea name="reason" placeholder="请输入操作原因" class="layui-textarea" lay-verify="required"></textarea>';
                actionHtml += '</div>';
                actionHtml += '</div>';
                actionHtml += '<input type="hidden" name="order_number" value="' + data.order_number + '">';
                actionHtml += '</form>';
                actionHtml += '</div>';

                layer.open({
                    type: 1,
                    title: '代付操作',
                    area: ['450px', '320px'],
                    content: actionHtml,
                    btn: ['确认操作', '取消'],
                    btnAlign: 'c',
                    success: function(layero, index) {
                        // 重新渲染表单
                        form.render();
                        console.log('Form rendered in layer');
                    },
                    yes: function(index, layero) {
                        console.log('Confirm button clicked');
                        // 获取表单数据
                        var actionType = $('input[name="action_type"]:checked').val();
                        var reason = $('textarea[name="reason"]').val();
                        var orderNumber = $('input[name="order_number"]').val();

                        console.log('Form data:', {actionType: actionType, reason: reason, orderNumber: orderNumber});

                        // 验证数据
                        if (!actionType) {
                            layer.msg('请选择操作类型');
                            return;
                        }
                        if (!reason || !reason.trim()) {
                            layer.msg('请输入操作原因');
                            return;
                        }

                        // 确认操作
                        var actionText = actionType === 'success' ? '支付成功' : '支付失败';
                        layer.confirm('确认标记订单 ' + orderNumber + ' 为' + actionText + '？', {
                            title: '确认操作'
                        }, function(confirmIndex) {
                            layer.close(confirmIndex);
                            layer.close(index); // 关闭操作弹窗

                            // 发送请求
                            var url = actionType === 'success' ?
                                '/manage/bank/markWithdrawalPaid' :
                                '/manage/bank/markWithdrawalFailed';

                            var loadIndex = layer.load(2);
                            $.post(url, {
                                order_number: orderNumber,
                                reason: reason.trim()
                            }, function(res) {
                                layer.close(loadIndex);
                                if (res.code == 1) {
                                    layer.msg('操作成功', {icon: 1});
                                    table.reload('present_record');
                                } else {
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }, 'json');
                        });
                    }
                });
            }
        });

        active = {
            search: function(){
                //执行重载
                table.reload('present_record', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
                        search_t: $("select[name='search_t'] option:selected").val()
                        ,search_c: $("input[name='search_c']").val()
                        ,state: $("select[name='state'] option:selected").val()
                        ,datetime_range: $("input[name='datetime_range']").val()
                        ,user_type:$('select[name=user_type] option:selected').val()
                    }
                    ,done: function(res, curr, count){
                        for (var i = 0; i < res.data.length; i++) {
                            if (res.data[i].statusStr == '处理中' || res.data[i].statusStr == '审核中' || res.data[i].statusStr == 'Reviewing') {
                                $("#myaudio")[0].play();
                                return false;
                            }
                        }
                    }
                }, 'data');
            }
        };

        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
<script src="/resource/js/manage/media.js"></script>
</body>
</html>
